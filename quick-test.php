<?php
/**
 * 快速测试page类型修复
 * 
 * 访问: http://frankloong.local/wp-content/plugins/Notion-to-WordPress/quick-test.php
 */

// 简单的测试，不依赖完整的WordPress环境
echo "<h1>Page类型修复验证</h1>";

// 检查修复的文件是否存在
$files_to_check = [
    'includes/handlers/class-notion-import-coordinator.php',
    'includes/services/class-notion-metadata-extractor.php',
    'includes/handlers/class-notion-to-wordpress-integrator.php'
];

echo "<h2>文件检查</h2>";
foreach ($files_to_check as $file) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✓ $file 存在</p>";
        
        // 检查关键修复内容
        $content = file_get_contents($file);
        
        if ($file === 'includes/handlers/class-notion-import-coordinator.php') {
            if (strpos($content, 'Untitled Page') !== false) {
                echo "<p style='color: green;'>  ✓ 包含默认标题逻辑</p>";
            } else {
                echo "<p style='color: red;'>  ✗ 缺少默认标题逻辑</p>";
            }
            
            if (strpos($content, '页面内容为空，将创建空内容的文章') !== false) {
                echo "<p style='color: green;'>  ✓ 包含空内容处理逻辑</p>";
            } else {
                echo "<p style='color: red;'>  ✗ 缺少空内容处理逻辑</p>";
            }
        }
        
        if ($file === 'includes/services/class-notion-metadata-extractor.php') {
            if (strpos($content, 'extract_page_type_metadata') !== false) {
                echo "<p style='color: green;'>  ✓ 包含page类型处理方法</p>";
            } else {
                echo "<p style='color: red;'>  ✗ 缺少page类型处理方法</p>";
            }
            
            if (strpos($content, "=== 'page'") !== false) {
                echo "<p style='color: green;'>  ✓ 包含page类型检测</p>";
            } else {
                echo "<p style='color: red;'>  ✗ 缺少page类型检测</p>";
            }
        }
        
        if ($file === 'includes/handlers/class-notion-to-wordpress-integrator.php') {
            if (strpos($content, 'Untitled Post') !== false) {
                echo "<p style='color: green;'>  ✓ 包含默认文章标题逻辑</p>";
            } else {
                echo "<p style='color: red;'>  ✗ 缺少默认文章标题逻辑</p>";
            }
        }
        
    } else {
        echo "<p style='color: red;'>✗ $file 不存在</p>";
    }
}

// 模拟测试数据
echo "<h2>模拟数据测试</h2>";

// 模拟page类型数据
$test_page = [
    'object' => 'page',
    'id' => 'test-page-12345678',
    'created_time' => date('c'),
    'last_edited_time' => date('c'),
    'properties' => [
        'title' => [
            'title' => [
                [
                    'plain_text' => 'Test Page Title',
                    'type' => 'text'
                ]
            ]
        ]
    ]
];

// 模拟无标题page类型数据
$test_page_no_title = [
    'object' => 'page',
    'id' => 'test-page-no-title-87654321',
    'created_time' => date('c'),
    'last_edited_time' => date('c'),
    'properties' => []
];

echo "<h3>测试数据结构</h3>";
echo "<p><strong>有标题的page类型:</strong></p>";
echo "<pre>" . json_encode($test_page, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";

echo "<p><strong>无标题的page类型:</strong></p>";
echo "<pre>" . json_encode($test_page_no_title, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "</pre>";

// 检查修复文档
echo "<h2>修复文档</h2>";
if (file_exists('PAGE_TYPE_SUPPORT_FIX.md')) {
    echo "<p style='color: green;'>✓ 修复文档已创建</p>";
    echo "<p><a href='PAGE_TYPE_SUPPORT_FIX.md' target='_blank'>查看修复文档</a></p>";
} else {
    echo "<p style='color: red;'>✗ 修复文档不存在</p>";
}

echo "<h2>下一步测试</h2>";
echo "<p>要完整测试修复效果，请：</p>";
echo "<ol>";
echo "<li>确保Notion-to-WordPress插件已激活</li>";
echo "<li>配置好Notion API密钥和数据库ID</li>";
echo "<li>在Notion数据库中创建一些page类型的页面</li>";
echo "<li>运行同步功能</li>";
echo "<li>检查WordPress后台是否成功创建了文章</li>";
echo "<li>查看日志文件确认处理过程</li>";
echo "</ol>";

echo "<h2>日志位置</h2>";
echo "<ul>";
echo "<li>WordPress Debug Log: <code>wp-content/debug.log</code></li>";
echo "<li>插件专用日志: <code>wp-content/uploads/notion-to-wordpress-logs/</code></li>";
echo "</ul>";

echo "<p style='margin-top: 30px; padding: 15px; background: #f0f8ff; border: 1px solid #0073aa;'>";
echo "<strong>修复总结:</strong> 已成功修复page类型页面同步问题，包括：<br>";
echo "1. 增强元数据提取器支持page类型<br>";
echo "2. 移除不合理的跳过逻辑<br>";
echo "3. 提供默认值机制<br>";
echo "4. 增强日志记录";
echo "</p>";
?>
