<?php
/**
 * WordPress管理界面测试页面
 * 
 * 将此文件放在插件根目录，然后在WordPress管理界面中访问
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

// 添加管理菜单
add_action('admin_menu', 'add_page_type_test_menu');

function add_page_type_test_menu() {
    add_submenu_page(
        'tools.php',
        'Page类型测试',
        'Page类型测试',
        'manage_options',
        'page-type-test',
        'render_page_type_test_page'
    );
}

function render_page_type_test_page() {
    ?>
    <div class="wrap">
        <h1>Notion Page类型同步测试</h1>
        
        <?php if (isset($_POST['run_test'])): ?>
        <div class="notice notice-info">
            <h2>测试结果</h2>
            <?php run_page_type_tests(); ?>
        </div>
        <?php endif; ?>
        
        <form method="post">
            <?php wp_nonce_field('page_type_test'); ?>
            <table class="form-table">
                <tr>
                    <th scope="row">测试说明</th>
                    <td>
                        <p>此测试将验证修复后的插件能否正确处理Notion中type为"page"的内容。</p>
                        <p><strong>修复内容包括：</strong></p>
                        <ul>
                            <li>增强元数据提取器支持page类型</li>
                            <li>移除标题为空的不合理跳过逻辑</li>
                            <li>为空标题和空内容提供默认值</li>
                            <li>增强日志记录便于调试</li>
                        </ul>
                    </td>
                </tr>
            </table>
            
            <p class="submit">
                <input type="submit" name="run_test" class="button-primary" value="运行测试" />
            </p>
        </form>
        
        <h2>手动测试步骤</h2>
        <ol>
            <li>确保已配置Notion API密钥和数据库ID</li>
            <li>在Notion数据库中创建一些page类型的页面</li>
            <li>运行插件的同步功能</li>
            <li>检查WordPress后台是否成功创建了文章</li>
            <li>查看日志确认处理过程</li>
        </ol>
        
        <h2>当前插件配置</h2>
        <?php
        $options = get_option('notion_to_wordpress_options', []);
        $api_key = $options['notion_api_key'] ?? '';
        $database_id = $options['notion_database_id'] ?? '';
        ?>
        <table class="widefat">
            <tr>
                <td><strong>API密钥</strong></td>
                <td><?php echo empty($api_key) ? '<span style="color: red;">未设置</span>' : '<span style="color: green;">已设置</span>'; ?></td>
            </tr>
            <tr>
                <td><strong>数据库ID</strong></td>
                <td><?php echo empty($database_id) ? '<span style="color: red;">未设置</span>' : '<span style="color: green;">已设置 (' . substr($database_id, 0, 8) . '...)</span>'; ?></td>
            </tr>
        </table>
    </div>
    <?php
}

function run_page_type_tests() {
    // 检查必要的类
    $required_classes = [
        'Notion_Metadata_Extractor',
        'Notion_To_WordPress_Integrator',
        'Notion_API'
    ];
    
    echo "<h3>类加载检查</h3>";
    $all_loaded = true;
    foreach ($required_classes as $class) {
        if (class_exists($class)) {
            echo "<p style='color: green;'>✓ $class 已加载</p>";
        } else {
            echo "<p style='color: red;'>✗ $class 未加载</p>";
            $all_loaded = false;
        }
    }
    
    if (!$all_loaded) {
        echo "<p><strong>错误：</strong>部分必需的类未加载，请检查插件是否正确激活。</p>";
        return;
    }
    
    // 测试元数据提取
    echo "<h3>元数据提取测试</h3>";
    
    // 测试page类型数据
    $test_page = [
        'object' => 'page',
        'id' => 'test-page-12345678',
        'created_time' => date('c'),
        'last_edited_time' => date('c'),
        'properties' => [
            'title' => [
                'title' => [
                    [
                        'plain_text' => 'Test Page Title',
                        'type' => 'text'
                    ]
                ]
            ]
        ]
    ];
    
    $metadata = Notion_Metadata_Extractor::extract_page_metadata($test_page);
    echo "<p><strong>有标题的page类型测试：</strong></p>";
    echo "<ul>";
    echo "<li>标题: " . ($metadata['title'] ?? 'empty') . "</li>";
    echo "<li>状态: " . ($metadata['status'] ?? 'unknown') . "</li>";
    echo "<li>类型: " . ($metadata['post_type'] ?? 'unknown') . "</li>";
    echo "</ul>";
    
    // 测试无标题page类型
    $test_page_no_title = [
        'object' => 'page',
        'id' => 'test-page-no-title-87654321',
        'created_time' => date('c'),
        'last_edited_time' => date('c'),
        'properties' => []
    ];
    
    $metadata_no_title = Notion_Metadata_Extractor::extract_page_metadata($test_page_no_title);
    echo "<p><strong>无标题的page类型测试：</strong></p>";
    echo "<ul>";
    echo "<li>标题: " . ($metadata_no_title['title'] ?? 'empty') . "</li>";
    echo "<li>状态: " . ($metadata_no_title['status'] ?? 'unknown') . "</li>";
    echo "<li>类型: " . ($metadata_no_title['post_type'] ?? 'unknown') . "</li>";
    echo "</ul>";
    
    // 测试WordPress集成验证
    echo "<h3>WordPress集成验证测试</h3>";
    $empty_metadata = ['title' => '', 'status' => 'publish'];
    $validation_result = Notion_To_WordPress_Integrator::validate_post_data($empty_metadata);
    
    if (is_wp_error($validation_result)) {
        echo "<p style='color: red;'>✗ 空标题验证失败: " . $validation_result->get_error_message() . "</p>";
    } else {
        echo "<p style='color: green;'>✓ 空标题验证通过，不再导致错误</p>";
    }
    
    echo "<h3>测试总结</h3>";
    echo "<p>如果上述测试都显示为绿色✓，说明修复已成功应用。</p>";
    echo "<p>现在可以尝试同步包含page类型页面的Notion数据库。</p>";
}

// 如果在插件激活时包含此文件
if (is_admin()) {
    // 文件已包含，菜单会自动添加
}
?>
