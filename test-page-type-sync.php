<?php
/**
 * 测试page类型页面同步功能
 *
 * 这个脚本用于验证修复后的插件能否正确处理Notion中type为"page"的内容
 *
 * 使用方法：
 * 1. 将此文件放在插件根目录
 * 2. 在浏览器中访问：http://frankloong.local/wp-content/plugins/Notion-to-WordPress/test-page-type-sync.php
 * 3. 或在命令行运行：php test-page-type-sync.php
 */

// 加载WordPress环境
if (!defined('ABSPATH')) {
    // 尝试加载WordPress
    $wp_load_paths = [
        dirname(__FILE__) . '/../../../wp-load.php',
        dirname(__FILE__) . '/../../../../wp-load.php',
        dirname(__FILE__) . '/../../../../../wp-load.php'
    ];

    $wp_loaded = false;
    foreach ($wp_load_paths as $path) {
        if (file_exists($path)) {
            require_once $path;
            $wp_loaded = true;
            break;
        }
    }

    if (!$wp_loaded) {
        die('无法加载WordPress环境。请确保此脚本在WordPress插件目录中运行。');
    }
}

// 模拟page类型页面数据
function create_test_page_data() {
    return [
        'object' => 'page',
        'id' => 'test-page-' . uniqid(),
        'created_time' => date('c'),
        'last_edited_time' => date('c'),
        'url' => 'https://notion.so/test-page',
        'properties' => [
            'title' => [
                'title' => [
                    [
                        'plain_text' => 'Test Page Title',
                        'type' => 'text'
                    ]
                ]
            ]
        ]
    ];
}

// 模拟没有标题的page类型页面
function create_test_page_without_title() {
    return [
        'object' => 'page',
        'id' => 'test-page-no-title-' . uniqid(),
        'created_time' => date('c'),
        'last_edited_time' => date('c'),
        'url' => 'https://notion.so/test-page-no-title',
        'properties' => []
    ];
}

// 测试元数据提取
function test_metadata_extraction() {
    echo "=== 测试元数据提取 ===\n";
    
    // 测试有标题的page类型
    $page_with_title = create_test_page_data();
    echo "测试有标题的page类型页面...\n";
    
    if (class_exists('Notion_Metadata_Extractor')) {
        $metadata = Notion_Metadata_Extractor::extract_page_metadata($page_with_title);
        echo "提取的标题: " . ($metadata['title'] ?? 'empty') . "\n";
        echo "页面状态: " . ($metadata['status'] ?? 'unknown') . "\n";
        echo "文章类型: " . ($metadata['post_type'] ?? 'unknown') . "\n";
    } else {
        echo "错误: Notion_Metadata_Extractor 类不存在\n";
    }
    
    echo "\n";
    
    // 测试没有标题的page类型
    $page_without_title = create_test_page_without_title();
    echo "测试没有标题的page类型页面...\n";
    
    if (class_exists('Notion_Metadata_Extractor')) {
        $metadata = Notion_Metadata_Extractor::extract_page_metadata($page_without_title);
        echo "提取的标题: " . ($metadata['title'] ?? 'empty') . "\n";
        echo "页面状态: " . ($metadata['status'] ?? 'unknown') . "\n";
        echo "文章类型: " . ($metadata['post_type'] ?? 'unknown') . "\n";
    } else {
        echo "错误: Notion_Metadata_Extractor 类不存在\n";
    }
    
    echo "\n";
}

// 测试WordPress集成
function test_wordpress_integration() {
    echo "=== 测试WordPress集成 ===\n";
    
    // 测试空标题的处理
    $empty_metadata = ['title' => '', 'status' => 'publish'];
    $test_content = '<p>Test content</p>';
    $test_page_id = 'test-page-integration';
    
    echo "测试空标题处理...\n";
    
    if (class_exists('Notion_To_WordPress_Integrator')) {
        // 测试验证逻辑
        $validation_result = Notion_To_WordPress_Integrator::validate_post_data($empty_metadata);
        if (is_wp_error($validation_result)) {
            echo "验证失败: " . $validation_result->get_error_message() . "\n";
        } else {
            echo "验证通过: 空标题不再导致验证失败\n";
        }
    } else {
        echo "错误: Notion_To_WordPress_Integrator 类不存在\n";
    }
    
    echo "\n";
}

// 实际测试Notion API和同步功能
function test_real_sync() {
    echo "=== 测试实际同步功能 ===\n";

    // 检查插件是否激活
    if (!is_plugin_active('Notion-to-WordPress/notion-to-wordpress.php')) {
        echo "警告: Notion-to-WordPress插件未激活\n";
    }

    // 获取插件配置
    $options = get_option('notion_to_wordpress_options', []);
    $api_key = $options['notion_api_key'] ?? '';
    $database_id = $options['notion_database_id'] ?? '';

    if (empty($api_key) || empty($database_id)) {
        echo "错误: 插件配置不完整\n";
        echo "API Key: " . (empty($api_key) ? '未设置' : '已设置') . "\n";
        echo "Database ID: " . (empty($database_id) ? '未设置' : '已设置') . "\n";
        return;
    }

    echo "插件配置检查通过\n";
    echo "开始测试Notion API连接...\n";

    try {
        // 创建API实例
        $notion_api = new Notion_API($api_key);

        // 测试获取数据库页面
        $pages = $notion_api->get_database_pages($database_id);
        echo "成功获取到 " . count($pages) . " 个页面\n";

        // 分析页面类型
        $page_types = [];
        $page_samples = [];

        foreach ($pages as $page) {
            $object_type = $page['object'] ?? 'unknown';
            $page_types[$object_type] = ($page_types[$object_type] ?? 0) + 1;

            // 收集样本用于测试
            if (count($page_samples) < 3) {
                $page_samples[] = $page;
            }
        }

        echo "\n页面类型分布:\n";
        foreach ($page_types as $type => $count) {
            echo "- $type: $count 个\n";
        }

        // 测试元数据提取
        echo "\n测试元数据提取:\n";
        foreach ($page_samples as $i => $page) {
            $page_id = $page['id'] ?? 'unknown';
            $object_type = $page['object'] ?? 'unknown';

            echo "\n样本 " . ($i + 1) . " (类型: $object_type, ID: " . substr($page_id, -8) . "):\n";

            $metadata = Notion_Metadata_Extractor::extract_page_metadata($page);
            echo "- 标题: " . ($metadata['title'] ?? 'empty') . "\n";
            echo "- 状态: " . ($metadata['status'] ?? 'unknown') . "\n";
            echo "- 类型: " . ($metadata['post_type'] ?? 'unknown') . "\n";
        }

    } catch (Exception $e) {
        echo "错误: " . $e->getMessage() . "\n";
    }
}

// 主测试函数
function run_tests() {
    echo "<pre>\n";
    echo "开始测试page类型页面同步功能...\n\n";

    // 检查WordPress环境
    if (!function_exists('is_plugin_active')) {
        require_once ABSPATH . 'wp-admin/includes/plugin.php';
    }

    // 检查必要的类是否存在
    $required_classes = [
        'Notion_Metadata_Extractor',
        'Notion_To_WordPress_Integrator',
        'Notion_Logger',
        'Notion_API'
    ];

    $missing_classes = [];
    foreach ($required_classes as $class) {
        if (!class_exists($class)) {
            $missing_classes[] = $class;
        }
    }

    if (!empty($missing_classes)) {
        echo "错误: 以下必需的类不存在:\n";
        foreach ($missing_classes as $class) {
            echo "- $class\n";
        }
        echo "\n请确保插件已正确加载。\n";
        return;
    }

    echo "所有必需的类都已加载 ✓\n\n";

    // 运行测试
    test_metadata_extraction();
    test_wordpress_integration();
    test_real_sync();

    echo "\n测试完成！\n";
    echo "\n如果看到任何错误，请检查修复是否正确应用。\n";
    echo "</pre>\n";
}

// Web界面支持
function render_web_interface() {
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>Notion-to-WordPress Page类型测试</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
            .success { color: green; }
            .error { color: red; }
            .warning { color: orange; }
            pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
            button { padding: 10px 20px; background: #0073aa; color: white; border: none; border-radius: 3px; cursor: pointer; }
            button:hover { background: #005a87; }
        </style>
    </head>
    <body>
        <h1>Notion-to-WordPress Page类型同步测试</h1>
        <p>此测试用于验证修复后的插件能否正确处理Notion中type为"page"的内容。</p>

        <div class="test-section">
            <h2>测试控制</h2>
            <form method="post">
                <button type="submit" name="run_test" value="1">运行测试</button>
                <button type="submit" name="clear_log" value="1">清除日志</button>
            </form>
        </div>

        <?php if (isset($_POST['run_test'])): ?>
        <div class="test-section">
            <h2>测试结果</h2>
            <?php
            ob_start();
            run_tests();
            $output = ob_get_clean();
            echo $output;
            ?>
        </div>
        <?php endif; ?>

        <?php if (isset($_POST['clear_log'])): ?>
        <div class="test-section">
            <h2>日志清除</h2>
            <p class="success">日志已清除（如果有的话）</p>
        </div>
        <?php endif; ?>

        <div class="test-section">
            <h2>修复说明</h2>
            <ul>
                <li><strong>元数据提取器增强</strong>：添加了page类型检测和专门的处理逻辑</li>
                <li><strong>移除不合理跳过</strong>：不再因为标题为空或内容为空而跳过页面</li>
                <li><strong>默认值机制</strong>：为空标题和空内容提供合理的默认值</li>
                <li><strong>增强日志</strong>：添加了详细的page类型处理日志</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>查看日志</h2>
            <p>如需查看详细的同步日志，请检查以下位置：</p>
            <ul>
                <li>WordPress Debug Log: <code>wp-content/debug.log</code></li>
                <li>插件专用日志: <code>wp-content/uploads/notion-to-wordpress-logs/</code></li>
            </ul>
        </div>
    </body>
    </html>
    <?php
}

// 检查运行环境
if (isset($_SERVER['HTTP_HOST'])) {
    // Web环境
    render_web_interface();
} else {
    // 命令行环境
    run_tests();
}
