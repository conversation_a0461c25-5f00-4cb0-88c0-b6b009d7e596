<?php
declare(strict_types=1);

/**
 * 插件引导文件
 * 
 * WordPress 读取此文件以在插件管理后台生成插件信息，
 * 此文件还包括插件使用的所有依赖项，注册激活和停用函数，并定义一个启动插件的函数。
 * 
 * @link              https://github.com/<PERSON>-<PERSON><PERSON>/Notion-to-WordPress
 * @since             1.0.9
 * @package           Notion_To_WordPress
 * @wordpress-plugin
 * Plugin Name:       Notion to WordPress
 * Plugin URI:        https://github.com/Frank-<PERSON><PERSON>/Notion-to-WordPress
 * Description:       从 Notion 数据库同步内容到 WordPress 文章，支持增量同步、定时同步和 Webhook 同步。
 * Version:           2.0.0-beta.1
 * Author:            <PERSON><PERSON><PERSON><PERSON>
 * Author URI:        https://github.com/Frank-Loong
 * License:           GPL-3.0-or-later
 * License URI:       https://www.gnu.org/licenses/gpl-3.0.txt
 * Text Domain:       notion-to-wordpress
 * Domain Path:       /languages
 */

// 如果直接访问此文件，则退出
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 主插件文件路径
 *
 * @since 1.1.0
 */
define( 'NOTION_TO_WORDPRESS_FILE', __FILE__ );

/**
 * 插件的当前版本号
 */
define( 'NOTION_TO_WORDPRESS_VERSION', '2.0.0-beta.1' );

/**
 * 性能模式常量 - 启用后将减少日志记录和统计收集
 */
if ( ! defined( 'NOTION_PERFORMANCE_MODE' ) ) {
    define( 'NOTION_PERFORMANCE_MODE', true ); // 默认启用性能模式
}

/**
 * Composer自动加载器
 *
 * 加载Composer生成的自动加载器，支持PSR-4命名空间和classmap
 * 这是现代PHP项目的标准做法，替代手动require_once
 */
$autoloader_path = plugin_dir_path( __FILE__ ) . 'vendor/autoload.php';
if ( file_exists( $autoloader_path ) ) {
    require_once $autoloader_path;

    // 记录自动加载器状态
    if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
        error_log( 'Notion-to-WordPress: Composer autoloader loaded successfully' );
    }
} else {
    // 如果autoloader不存在，记录错误但不阻止插件运行（向后兼容）
    if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
        error_log( 'Notion-to-WordPress: Composer autoloader not found, falling back to manual loading' );
    }
}

/**
 * Core层类别名映射（向后兼容）
 *
 * 为重构后的NTWP\Core命名空间类建立向后兼容的别名
 * 确保现有代码中的类引用继续正常工作
 *
 * 注意：目前只映射已完成PSR-4重构的类
 */
if ( file_exists( $autoloader_path ) ) {
    // 只为已完成PSR-4重构的Core层基础设施类建立别名
    if ( class_exists( 'NTWP\\Core\\Logger' ) ) {
        class_alias( 'NTWP\\Core\\Logger', 'Notion_Logger' );
    }
    if ( class_exists( 'NTWP\\Core\\Security' ) ) {
        class_alias( 'NTWP\\Core\\Security', 'Notion_Security' );
    }
    if ( class_exists( 'NTWP\\Core\\Text_Processor' ) ) {
        class_alias( 'NTWP\\Core\\Text_Processor', 'Notion_Text_Processor' );
    }
    if ( class_exists( 'NTWP\\Core\\HTTP_Client' ) ) {
        class_alias( 'NTWP\\Core\\HTTP_Client', 'Notion_HTTP_Client' );
    }
    if ( class_exists( 'NTWP\\Core\\Dependency_Container' ) ) {
        class_alias( 'NTWP\\Core\\Dependency_Container', 'Notion_Dependency_Container' );
    }
    if ( class_exists( 'NTWP\\Core\\Performance_Monitor' ) ) {
        class_alias( 'NTWP\\Core\\Performance_Monitor', 'Notion_Performance_Monitor' );
    }
    if ( class_exists( 'NTWP\\Core\\Memory_Manager' ) ) {
        class_alias( 'NTWP\\Core\\Memory_Manager', 'Notion_Memory_Manager' );
    }

    // 为依赖注入容器中的接口和抽象类建立别名
    if ( interface_exists( 'NTWP\\Core\\Service_Interface' ) ) {
        class_alias( 'NTWP\\Core\\Service_Interface', 'Notion_Service_Interface' );
    }
    if ( class_exists( 'NTWP\\Core\\Abstract_Service' ) ) {
        class_alias( 'NTWP\\Core\\Abstract_Service', 'Notion_Abstract_Service' );
    }

    // 记录类别名映射状态
    if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
        error_log( 'Notion-to-WordPress: Core layer class aliases established successfully' );
    }
}

/**
 * 核心依赖加载（临时保留，后续阶段将移除）
 *
 * 注意：这些手动require_once语句将在重构完成后移除
 * 目前保留以确保向后兼容性和渐进式迁移
 */
require_once plugin_dir_path( __FILE__ ) . 'includes/utils/class-notion-to-wordpress-helper.php';
require_once plugin_dir_path( __FILE__ ) . 'includes/utils/class-notion-database-helper.php';
require_once plugin_dir_path( __FILE__ ) . 'includes/utils/class-notion-database-index-manager.php';
require_once plugin_dir_path( __FILE__ ) . 'includes/utils/class-notion-smart-cache.php';
require_once plugin_dir_path( __FILE__ ) . 'includes/utils/class-notion-unified-concurrency-manager.php';
// require_once plugin_dir_path( __FILE__ ) . 'includes/core/class-notion-dependency-container.php'; // 已迁移到PSR-4
// require_once plugin_dir_path( __FILE__ ) . 'includes/services/class-notion-service-layer.php'; // 暂时注释，等待Services层重构
// require_once plugin_dir_path( __FILE__ ) . 'includes/core/class-notion-memory-manager.php'; // 已迁移到PSR-4
// require_once plugin_dir_path( __FILE__ ) . 'includes/core/class-notion-logger.php'; // 已迁移到PSR-4
// require_once plugin_dir_path( __FILE__ ) . 'includes/core/class-notion-performance-monitor.php'; // 已迁移到PSR-4

// 现代化异步处理系统（已迁移到PSR-4）
// require_once plugin_dir_path( __FILE__ ) . 'includes/core/class-notion-task-queue.php'; // 已迁移到PSR-4
// require_once plugin_dir_path( __FILE__ ) . 'includes/core/class-notion-task-executor.php'; // 已迁移到PSR-4
// require_once plugin_dir_path( __FILE__ ) . 'includes/core/class-notion-progress-tracker.php'; // 已迁移到PSR-4
// require_once plugin_dir_path( __FILE__ ) . 'includes/core/class-notion-async-task-scheduler.php'; // 已迁移到PSR-4
// require_once plugin_dir_path( __FILE__ ) . 'includes/core/class-notion-modern-async-engine.php'; // 已迁移到PSR-4

// require_once plugin_dir_path( __FILE__ ) . 'includes/core/class-notion-algorithm-optimizer.php'; // 已迁移到PSR-4

require_once plugin_dir_path( __FILE__ ) . 'includes/services/class-notion-incremental-detector.php';
require_once plugin_dir_path( __FILE__ ) . 'includes/utils/class-notion-config-simplifier.php';

// 修复：加载关键的流式处理器类
require_once plugin_dir_path( __FILE__ ) . 'includes/utils/class-notion-stream-processor.php';
require_once plugin_dir_path( __FILE__ ) . 'includes/utils/class-notion-concurrent-network-manager.php';
require_once plugin_dir_path( __FILE__ ) . 'includes/utils/class-notion-network-retry.php';
require_once plugin_dir_path( __FILE__ ) . 'includes/utils/class-notion-smart-api-merger.php';
require_once plugin_dir_path( __FILE__ ) . 'includes/utils/class-notion-async-helper.php';
require_once plugin_dir_path( __FILE__ ) . 'includes/framework/class-notion-to-wordpress.php';

/**
 * 插件激活时运行的代码
 * 此操作的文档位于 includes/class-notion-to-wordpress.php
 */
function activate_notion_to_wordpress() {
	// 初始化现代异步处理系统
	init_modern_async_system();

	Notion_To_WordPress::activate();
}

/**
 * 初始化现代异步处理系统
 *
 * @since 2.0.0-beta.2
 */
function init_modern_async_system() {
	// 记录现代异步系统启动
	if (class_exists('Notion_Logger')) {
		Notion_Logger::info_log('现代异步处理系统已启动，无需外部依赖', 'Plugin Activation');
	}
}

/**
 * 插件停用时运行的代码
 * 此操作的文档位于 includes/class-notion-to-wordpress.php
 */
function deactivate_notion_to_wordpress() {
	// Clear any scheduled cron jobs
	wp_clear_scheduled_hook('notion_cron_import');
	wp_clear_scheduled_hook('notion_to_wordpress_cron_update');
	wp_clear_scheduled_hook('notion_to_wordpress_log_cleanup');
}

register_activation_hook( NOTION_TO_WORDPRESS_FILE, 'activate_notion_to_wordpress' );
register_deactivation_hook( NOTION_TO_WORDPRESS_FILE, 'deactivate_notion_to_wordpress' );

/**
 * 开始执行插件
 *
 * 由于插件中的所有内容都通过钩子注册，
 * 因此从文件的这一点开始启动插件不会影响页面生命周期。
 *
 * @since    1.0.0
 */
function run_notion_to_wordpress() {
	$plugin = new Notion_To_WordPress();
	$plugin->run();
}

run_notion_to_wordpress(); 